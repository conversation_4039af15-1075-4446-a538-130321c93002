2025-08-04 21:21:49,268 - INFO - ================================================================================
2025-08-04 21:21:49,268 - INFO - 🚀 微信公众号全自动爬取流程启动 🚀
2025-08-04 21:21:49,268 - INFO - ================================================================================
2025-08-04 21:21:49,268 - INFO - 版本: v3.0 - 全自动化版本
2025-08-04 21:21:49,268 - INFO - 设计用途: Windows任务计划程序自动执行
2025-08-04 21:21:49,268 - INFO - 执行时间: 2025-08-04 21:21:49
2025-08-04 21:21:49,268 - INFO - ================================================================================
2025-08-04 21:21:49,270 - INFO - 启动全新自动化爬取流程...
2025-08-04 21:21:49,270 - INFO - ================================================================================
2025-08-04 21:21:49,270 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-04 21:21:49,270 - INFO - ================================================================================
2025-08-04 21:21:49,270 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-04 21:21:49,346 - INFO - 找到有效目标 1: 钟山清风 - https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-04 21:21:49,346 - INFO - 找到有效目标 2: 南京党建 - http://mp.weixin.qq.com/s?__biz=MzI2OTMwNzU5Nw==&m...
2025-08-04 21:21:49,346 - INFO - 找到有效目标 3: 南京发布 - http://mp.weixin.qq.com/s?__biz=MjM5MTczODg0MA==&m...
2025-08-04 21:21:49,347 - INFO - 找到有效目标 4: 南京社会工作 - http://mp.weixin.qq.com/s?__biz=MzkzNjY5OTc1MA==&m...
2025-08-04 21:21:49,347 - INFO - 找到有效目标 5: 网信南京 - http://mp.weixin.qq.com/s?__biz=MzIyMTU5NzkxMQ==&m...
2025-08-04 21:21:49,347 - INFO - 找到有效目标 6: 南京市保密协会 - http://mp.weixin.qq.com/s?__biz=MzIxNDYzMDU3Mg==&m...
2025-08-04 21:21:49,347 - INFO - 找到有效目标 7: 南京市中级人民法院 - http://mp.weixin.qq.com/s?__biz=MzA5OTIwMDMyNg==&m...
2025-08-04 21:21:49,347 - INFO - 找到有效目标 8: 南京检察 - http://mp.weixin.qq.com/s?__biz=MzA4NDIwNDMwMA==&m...
2025-08-04 21:21:49,348 - INFO - 找到有效目标 9: 南京市发展和改革委员会 - http://mp.weixin.qq.com/s?__biz=MzIxNzc1ODMwOA==&m...
2025-08-04 21:21:49,348 - INFO - 找到有效目标 10: 南京教育发布 - http://mp.weixin.qq.com/s?__biz=MzI2MDEwMTQzNw==&m...
2025-08-04 21:21:49,348 - INFO - 找到有效目标 11: 南京市工信局 - http://mp.weixin.qq.com/s?__biz=MzI1OTE2NzUxNQ==&m...
2025-08-04 21:21:49,348 - INFO - 找到有效目标 12: 南京警方 - http://mp.weixin.qq.com/s?__biz=MzI2NDIzMzc3Nw==&m...
2025-08-04 21:21:49,348 - INFO - 找到有效目标 13: 南京人社 - http://mp.weixin.qq.com/s?__biz=MzkyMDI5ODI2NA==&m...
2025-08-04 21:21:49,348 - INFO - 找到有效目标 14: 南京生态环境 - http://mp.weixin.qq.com/s?__biz=MzA5OTY3NTMxNg==&m...
2025-08-04 21:21:49,348 - INFO - 找到有效目标 15: 南京交通 - http://mp.weixin.qq.com/s?__biz=MzIyMzIwMjQ3MA==&m...
2025-08-04 21:21:49,348 - INFO - 找到有效目标 16: 南京水务 - http://mp.weixin.qq.com/s?__biz=MjM5MDM3MTg4NQ==&m...
2025-08-04 21:21:49,348 - INFO - 找到有效目标 17: 南京市城管局 - http://mp.weixin.qq.com/s?__biz=MzI3OTM3MDkzNA==&m...
2025-08-04 21:21:49,348 - INFO - 找到有效目标 18: 南京美丽乡村 - http://mp.weixin.qq.com/s?__biz=MzIxMDUwMDUwMQ==&m...
2025-08-04 21:21:49,348 - INFO - 找到有效目标 19: 南京应急管理 - http://mp.weixin.qq.com/s?__biz=MzI0NjQwMjE4Ng==&m...
2025-08-04 21:21:49,349 - INFO - 找到有效目标 20: 南京市数据局 - http://mp.weixin.qq.com/s?__biz=MzU0MTY4NzM5OQ==&m...
2025-08-04 21:21:49,349 - INFO - 找到有效目标 21: 南京市场监管 - http://mp.weixin.qq.com/s?__biz=MjM5Njk4NTg1OA==&m...
2025-08-04 21:21:49,349 - INFO - 找到有效目标 22: 南京市信访局 - http://mp.weixin.qq.com/s?__biz=MzIxNDcxNzA4OA==&m...
2025-08-04 21:21:49,349 - INFO - 找到有效目标 23: 南京税务 - http://mp.weixin.qq.com/s?__biz=MjM5NTkxNDU5Nw==&m...
2025-08-04 21:21:49,349 - INFO - 找到有效目标 24: 南京消防 - http://mp.weixin.qq.com/s?__biz=MjM5OTA1OTkyNA==&m...
2025-08-04 21:21:49,349 - INFO - 找到有效目标 25: 南京工会会员 - http://mp.weixin.qq.com/s?__biz=MzI5NjIyNDU4Mg==&m...
2025-08-04 21:21:49,349 - INFO - 找到有效目标 26: 青春南京 - http://mp.weixin.qq.com/s?__biz=MjM5NDg4NzAyMQ==&m...
2025-08-04 21:21:49,349 - INFO - 找到有效目标 27: 南京妇联 - http://mp.weixin.qq.com/s?__biz=MjM5NDA3NjExMA==&m...
2025-08-04 21:21:49,349 - INFO - 找到有效目标 28: 新宁商 - http://mp.weixin.qq.com/s?__biz=MzAwNzE3NjgwMA==&m...
2025-08-04 21:21:49,350 - INFO - 找到有效目标 29: 南京市残疾人联合会 - http://mp.weixin.qq.com/s?__biz=MzU2MTQ2MDE5OQ==&m...
2025-08-04 21:21:49,350 - INFO - 找到有效目标 30: 南京地铁 - http://mp.weixin.qq.com/s?__biz=MjM5NTU2MzY5Mg==&m...
2025-08-04 21:21:49,350 - INFO - 找到有效目标 31: 南京市城建控股公司 - http://mp.weixin.qq.com/s?__biz=MzU5NDk3Nzc4MQ==&m...
2025-08-04 21:21:49,350 - INFO - 找到有效目标 32: 南京市交通集团 - http://mp.weixin.qq.com/s?__biz=MzI2NDMwMTc0Mg==&m...
2025-08-04 21:21:49,350 - INFO - 找到有效目标 33: 河西集团 - http://mp.weixin.qq.com/s?__biz=MzIxOTI5OTQ2Nw==&m...
2025-08-04 21:21:49,350 - INFO - 找到有效目标 34: 南京水务集团有限公司 - http://mp.weixin.qq.com/s?__biz=MjM5Njg1ODg4OQ==&m...
2025-08-04 21:21:49,350 - INFO - 找到有效目标 35: 南京大学 - http://mp.weixin.qq.com/s?__biz=MzAxODAzMjQ1NQ==&m...
2025-08-04 21:21:49,350 - INFO - 找到有效目标 36: 东南大学 - http://mp.weixin.qq.com/s?__biz=MjM5NjQxMDE2MQ==&m...
2025-08-04 21:21:49,350 - INFO - 找到有效目标 37: 南京工业大学 - http://mp.weixin.qq.com/s?__biz=MzA4MTU0OTUwMg==&m...
2025-08-04 21:21:49,350 - INFO - 找到有效目标 38: 河海大学 - http://mp.weixin.qq.com/s?__biz=MzA4MTY3MzgwMw==&m...
2025-08-04 21:21:49,350 - INFO - 找到有效目标 39: 南京师范大学 - http://mp.weixin.qq.com/s?__biz=MzA5NDEyNDIzOQ==&m...
2025-08-04 21:21:49,350 - INFO - 找到有效目标 40: 法润玄武 - http://mp.weixin.qq.com/s?__biz=MzI0MDU1MjYxOQ==&m...
2025-08-04 21:21:49,350 - INFO - 找到有效目标 41: 秦淮司法行政 - http://mp.weixin.qq.com/s?__biz=MzIzNjM4ODU5NA==&m...
2025-08-04 21:21:49,351 - INFO - 找到有效目标 42: 南京建邺司法 - http://mp.weixin.qq.com/s?__biz=MzI0MTQ1MDk3Ng==&m...
2025-08-04 21:21:49,351 - INFO - 找到有效目标 43: 南京鼓楼政法 - http://mp.weixin.qq.com/s?__biz=MzkwMDE3NDY2NQ==&m...
2025-08-04 21:21:49,351 - INFO - 找到有效目标 44: 法润栖霞 - http://mp.weixin.qq.com/s?__biz=MzIwMjM1NTc1Mw==&m...
2025-08-04 21:21:49,351 - INFO - 找到有效目标 45: 雨花司法 - http://mp.weixin.qq.com/s?__biz=MzI4MTMwNDY3NA==&m...
2025-08-04 21:21:49,351 - INFO - 找到有效目标 46: 江宁司法 - http://mp.weixin.qq.com/s?__biz=MjM5MDE4MDI0Nw==&m...
2025-08-04 21:21:49,351 - INFO - 找到有效目标 47: 浦口普法 - http://mp.weixin.qq.com/s?__biz=MzAwNDUwODY5Mg==&m...
2025-08-04 21:21:49,351 - INFO - 找到有效目标 48: 六合智慧普法 - http://mp.weixin.qq.com/s?__biz=MzIwMjIwNjg1NA==&m...
2025-08-04 21:21:49,351 - INFO - 找到有效目标 49: 溧水普法 - http://mp.weixin.qq.com/s?__biz=MzI1MTIwMDE1Mg==&m...
2025-08-04 21:21:49,351 - INFO - 找到有效目标 50: 高淳司法 - http://mp.weixin.qq.com/s?__biz=MzAwODQ3MTcxNQ==&m...
2025-08-04 21:21:49,351 - INFO - 找到有效目标 51: 江北新区综合治理 - http://mp.weixin.qq.com/s?__biz=MzU5NzgzNDIxMg==&m...
2025-08-04 21:21:49,351 - INFO - 找到有效目标 52: 中建安装南京公司 - http://mp.weixin.qq.com/s?__biz=MzU4ODQ1NjgxNQ==&m...
2025-08-04 21:21:49,351 - INFO - 找到有效目标 53: 扬子国投 - http://mp.weixin.qq.com/s?__biz=MzA4ODQzMzY3OQ==&m...
2025-08-04 21:21:49,352 - INFO - 共找到 53 个有效的公众号目标
2025-08-04 21:21:49,352 - INFO - 📋 共找到 53 个公众号，开始逐个处理...
2025-08-04 21:21:49,352 - INFO - ============================================================
2025-08-04 21:21:49,352 - INFO - 📍 处理第 1/53 个公众号: 钟山清风
2025-08-04 21:21:49,352 - INFO - ============================================================
2025-08-04 21:21:49,352 - INFO - [步骤 1/5] 为 '钟山清风' 创建独立的 Cookie 抓取器...
2025-08-04 21:21:49,352 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 21:21:49,352 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 21:21:49,352 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 21:21:49,352 - INFO - === 开始重置网络状态 ===
2025-08-04 21:21:49,352 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 21:21:49,449 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 21:21:49,449 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 21:21:49,449 - INFO - 系统代理已成功关闭
2025-08-04 21:21:49,450 - INFO - ✅ 代理关闭操作
2025-08-04 21:21:49,450 - INFO - 🔗 正在验证网络连接...
2025-08-04 21:21:50,369 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 21:21:50,369 - INFO - ✅ 网络状态重置验证完成
2025-08-04 21:21:50,369 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 21:21:50,370 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 21:21:50,370 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider_gitlab\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 21:21:50,881 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 21:21:50,882 - INFO - 🔄 Cookie抓取器进程已启动，PID: 15512
2025-08-04 21:21:50,882 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 21:21:53,882 - INFO - 等待代理服务启动...
2025-08-04 21:22:25,304 - ERROR - 代理服务启动超时
2025-08-04 21:22:25,305 - ERROR - ❌ 代理服务无法正常启动
2025-08-04 21:22:25,305 - ERROR - 进程仍在运行，但代理服务无响应
2025-08-04 21:22:25,305 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 21:22:25,306 - INFO - 正在停止Cookie抓取器 (PID: 15512)...
2025-08-04 21:22:25,309 - INFO - Cookie抓取器已成功终止。
2025-08-04 21:22:25,310 - INFO - 正在验证并清理代理设置...
2025-08-04 21:22:25,311 - INFO - === 开始重置网络状态 ===
2025-08-04 21:22:25,311 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 21:22:25,401 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 21:22:25,401 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 21:22:25,401 - INFO - 系统代理已成功关闭
2025-08-04 21:22:25,401 - INFO - ✅ 代理关闭操作
2025-08-04 21:22:25,401 - INFO - 🔗 正在验证网络连接...
2025-08-04 21:22:26,351 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 21:22:26,352 - INFO - ✅ 网络状态重置验证完成
2025-08-04 21:22:26,352 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 21:22:28,544 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 21:22:28,545 - INFO - ✅ 网络连接验证正常
2025-08-04 21:22:28,545 - ERROR - ❌ 公众号 '钟山清风' Cookie 抓取器启动失败，跳过此公众号
2025-08-04 21:22:28,546 - INFO - ============================================================
2025-08-04 21:22:28,546 - INFO - 📍 处理第 2/53 个公众号: 南京党建
2025-08-04 21:22:28,546 - INFO - ============================================================
2025-08-04 21:22:28,546 - INFO - [步骤 1/5] 为 '南京党建' 创建独立的 Cookie 抓取器...
2025-08-04 21:22:28,546 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 21:22:28,547 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 21:22:28,547 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 21:22:28,547 - INFO - === 开始重置网络状态 ===
2025-08-04 21:22:28,547 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 21:22:28,628 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 21:22:28,628 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 21:22:28,628 - INFO - 系统代理已成功关闭
2025-08-04 21:22:28,629 - INFO - ✅ 代理关闭操作
2025-08-04 21:22:28,629 - INFO - 🔗 正在验证网络连接...
2025-08-04 21:22:32,643 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 21:22:32,643 - INFO - ✅ 网络状态重置验证完成
2025-08-04 21:22:32,644 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 21:22:32,644 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 21:22:32,645 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider_gitlab\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 21:22:33,148 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 21:22:33,149 - INFO - 🔄 Cookie抓取器进程已启动，PID: 22036
2025-08-04 21:22:33,149 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 21:22:36,149 - INFO - 等待代理服务启动...
