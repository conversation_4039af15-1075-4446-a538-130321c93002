2025-08-05 09:11:04,397 - INFO - ================================================================================
2025-08-05 09:11:04,397 - INFO - 🚀 微信公众号全自动爬取流程启动 🚀
2025-08-05 09:11:04,397 - INFO - ================================================================================
2025-08-05 09:11:04,398 - INFO - 版本: v3.0 - 全自动化版本
2025-08-05 09:11:04,398 - INFO - 设计用途: Windows任务计划程序自动执行
2025-08-05 09:11:04,398 - INFO - 执行时间: 2025-08-05 09:11:04
2025-08-05 09:11:04,398 - INFO - ================================================================================
2025-08-05 09:11:04,398 - INFO - 启动全新自动化爬取流程...
2025-08-05 09:11:04,398 - INFO - ================================================================================
2025-08-05 09:11:04,398 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-05 09:11:04,399 - INFO - ================================================================================
2025-08-05 09:11:04,399 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-05 09:11:06,211 - INFO - 找到有效目标 1: 钟山清风 - https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-05 09:11:06,211 - INFO - 找到有效目标 2: 南京党建 - http://mp.weixin.qq.com/s?__biz=MzI2OTMwNzU5Nw==&m...
2025-08-05 09:11:06,212 - INFO - 找到有效目标 3: 南京发布 - http://mp.weixin.qq.com/s?__biz=MjM5MTczODg0MA==&m...
2025-08-05 09:11:06,212 - INFO - 找到有效目标 4: 南京社会工作 - http://mp.weixin.qq.com/s?__biz=MzkzNjY5OTc1MA==&m...
2025-08-05 09:11:06,212 - INFO - 找到有效目标 5: 网信南京 - http://mp.weixin.qq.com/s?__biz=MzIyMTU5NzkxMQ==&m...
2025-08-05 09:11:06,212 - INFO - 找到有效目标 6: 南京市保密协会 - http://mp.weixin.qq.com/s?__biz=MzIxNDYzMDU3Mg==&m...
2025-08-05 09:11:06,212 - INFO - 找到有效目标 7: 南京市中级人民法院 - http://mp.weixin.qq.com/s?__biz=MzA5OTIwMDMyNg==&m...
2025-08-05 09:11:06,213 - INFO - 找到有效目标 8: 南京检察 - http://mp.weixin.qq.com/s?__biz=MzA4NDIwNDMwMA==&m...
2025-08-05 09:11:06,213 - INFO - 找到有效目标 9: 南京市发展和改革委员会 - http://mp.weixin.qq.com/s?__biz=MzIxNzc1ODMwOA==&m...
2025-08-05 09:11:06,213 - INFO - 找到有效目标 10: 南京教育发布 - http://mp.weixin.qq.com/s?__biz=MzI2MDEwMTQzNw==&m...
2025-08-05 09:11:06,213 - INFO - 找到有效目标 11: 南京市工信局 - http://mp.weixin.qq.com/s?__biz=MzI1OTE2NzUxNQ==&m...
2025-08-05 09:11:06,213 - INFO - 找到有效目标 12: 南京警方 - http://mp.weixin.qq.com/s?__biz=MzI2NDIzMzc3Nw==&m...
2025-08-05 09:11:06,213 - INFO - 找到有效目标 13: 南京人社 - http://mp.weixin.qq.com/s?__biz=MzkyMDI5ODI2NA==&m...
2025-08-05 09:11:06,214 - INFO - 找到有效目标 14: 南京生态环境 - http://mp.weixin.qq.com/s?__biz=MzA5OTY3NTMxNg==&m...
2025-08-05 09:11:06,214 - INFO - 找到有效目标 15: 南京交通 - http://mp.weixin.qq.com/s?__biz=MzIyMzIwMjQ3MA==&m...
2025-08-05 09:11:06,214 - INFO - 找到有效目标 16: 南京水务 - http://mp.weixin.qq.com/s?__biz=MjM5MDM3MTg4NQ==&m...
2025-08-05 09:11:06,214 - INFO - 找到有效目标 17: 南京市城管局 - http://mp.weixin.qq.com/s?__biz=MzI3OTM3MDkzNA==&m...
2025-08-05 09:11:06,214 - INFO - 找到有效目标 18: 南京美丽乡村 - http://mp.weixin.qq.com/s?__biz=MzIxMDUwMDUwMQ==&m...
2025-08-05 09:11:06,214 - INFO - 找到有效目标 19: 南京应急管理 - http://mp.weixin.qq.com/s?__biz=MzI0NjQwMjE4Ng==&m...
2025-08-05 09:11:06,214 - INFO - 找到有效目标 20: 南京市数据局 - http://mp.weixin.qq.com/s?__biz=MzU0MTY4NzM5OQ==&m...
2025-08-05 09:11:06,215 - INFO - 找到有效目标 21: 南京市场监管 - http://mp.weixin.qq.com/s?__biz=MjM5Njk4NTg1OA==&m...
2025-08-05 09:11:06,215 - INFO - 找到有效目标 22: 南京市信访局 - http://mp.weixin.qq.com/s?__biz=MzIxNDcxNzA4OA==&m...
2025-08-05 09:11:06,215 - INFO - 找到有效目标 23: 南京税务 - http://mp.weixin.qq.com/s?__biz=MjM5NTkxNDU5Nw==&m...
2025-08-05 09:11:06,215 - INFO - 找到有效目标 24: 南京消防 - http://mp.weixin.qq.com/s?__biz=MjM5OTA1OTkyNA==&m...
2025-08-05 09:11:06,215 - INFO - 找到有效目标 25: 南京工会会员 - http://mp.weixin.qq.com/s?__biz=MzI5NjIyNDU4Mg==&m...
2025-08-05 09:11:06,215 - INFO - 找到有效目标 26: 青春南京 - http://mp.weixin.qq.com/s?__biz=MjM5NDg4NzAyMQ==&m...
2025-08-05 09:11:06,215 - INFO - 找到有效目标 27: 南京妇联 - http://mp.weixin.qq.com/s?__biz=MjM5NDA3NjExMA==&m...
2025-08-05 09:11:06,215 - INFO - 找到有效目标 28: 新宁商 - http://mp.weixin.qq.com/s?__biz=MzAwNzE3NjgwMA==&m...
2025-08-05 09:11:06,215 - INFO - 找到有效目标 29: 南京市残疾人联合会 - http://mp.weixin.qq.com/s?__biz=MzU2MTQ2MDE5OQ==&m...
2025-08-05 09:11:06,215 - INFO - 找到有效目标 30: 南京地铁 - http://mp.weixin.qq.com/s?__biz=MjM5NTU2MzY5Mg==&m...
2025-08-05 09:11:06,216 - INFO - 找到有效目标 31: 南京市城建控股公司 - http://mp.weixin.qq.com/s?__biz=MzU5NDk3Nzc4MQ==&m...
2025-08-05 09:11:06,216 - INFO - 找到有效目标 32: 南京市交通集团 - http://mp.weixin.qq.com/s?__biz=MzI2NDMwMTc0Mg==&m...
2025-08-05 09:11:06,216 - INFO - 找到有效目标 33: 河西集团 - http://mp.weixin.qq.com/s?__biz=MzIxOTI5OTQ2Nw==&m...
2025-08-05 09:11:06,216 - INFO - 找到有效目标 34: 南京水务集团有限公司 - http://mp.weixin.qq.com/s?__biz=MjM5Njg1ODg4OQ==&m...
2025-08-05 09:11:06,216 - INFO - 找到有效目标 35: 南京大学 - http://mp.weixin.qq.com/s?__biz=MzAxODAzMjQ1NQ==&m...
2025-08-05 09:11:06,216 - INFO - 找到有效目标 36: 东南大学 - http://mp.weixin.qq.com/s?__biz=MjM5NjQxMDE2MQ==&m...
2025-08-05 09:11:06,216 - INFO - 找到有效目标 37: 南京工业大学 - http://mp.weixin.qq.com/s?__biz=MzA4MTU0OTUwMg==&m...
2025-08-05 09:11:06,216 - INFO - 找到有效目标 38: 河海大学 - http://mp.weixin.qq.com/s?__biz=MzA4MTY3MzgwMw==&m...
2025-08-05 09:11:06,217 - INFO - 找到有效目标 39: 南京师范大学 - http://mp.weixin.qq.com/s?__biz=MzA5NDEyNDIzOQ==&m...
2025-08-05 09:11:06,217 - INFO - 找到有效目标 40: 法润玄武 - http://mp.weixin.qq.com/s?__biz=MzI0MDU1MjYxOQ==&m...
2025-08-05 09:11:06,217 - INFO - 找到有效目标 41: 秦淮司法行政 - http://mp.weixin.qq.com/s?__biz=MzIzNjM4ODU5NA==&m...
2025-08-05 09:11:06,217 - INFO - 找到有效目标 42: 南京建邺司法 - http://mp.weixin.qq.com/s?__biz=MzI0MTQ1MDk3Ng==&m...
2025-08-05 09:11:06,218 - INFO - 找到有效目标 43: 南京鼓楼政法 - http://mp.weixin.qq.com/s?__biz=MzkwMDE3NDY2NQ==&m...
2025-08-05 09:11:06,218 - INFO - 找到有效目标 44: 法润栖霞 - http://mp.weixin.qq.com/s?__biz=MzIwMjM1NTc1Mw==&m...
2025-08-05 09:11:06,218 - INFO - 找到有效目标 45: 雨花司法 - http://mp.weixin.qq.com/s?__biz=MzI4MTMwNDY3NA==&m...
2025-08-05 09:11:06,218 - INFO - 找到有效目标 46: 江宁司法 - http://mp.weixin.qq.com/s?__biz=MjM5MDE4MDI0Nw==&m...
2025-08-05 09:11:06,218 - INFO - 找到有效目标 47: 浦口普法 - http://mp.weixin.qq.com/s?__biz=MzAwNDUwODY5Mg==&m...
2025-08-05 09:11:06,218 - INFO - 找到有效目标 48: 六合智慧普法 - http://mp.weixin.qq.com/s?__biz=MzIwMjIwNjg1NA==&m...
2025-08-05 09:11:06,218 - INFO - 找到有效目标 49: 溧水普法 - http://mp.weixin.qq.com/s?__biz=MzI1MTIwMDE1Mg==&m...
2025-08-05 09:11:06,218 - INFO - 找到有效目标 50: 高淳司法 - http://mp.weixin.qq.com/s?__biz=MzAwODQ3MTcxNQ==&m...
2025-08-05 09:11:06,218 - INFO - 找到有效目标 51: 江北新区综合治理 - http://mp.weixin.qq.com/s?__biz=MzU5NzgzNDIxMg==&m...
2025-08-05 09:11:06,218 - INFO - 找到有效目标 52: 中建安装南京公司 - http://mp.weixin.qq.com/s?__biz=MzU4ODQ1NjgxNQ==&m...
2025-08-05 09:11:06,218 - INFO - 找到有效目标 53: 扬子国投 - http://mp.weixin.qq.com/s?__biz=MzA4ODQzMzY3OQ==&m...
2025-08-05 09:11:06,219 - INFO - 共找到 53 个有效的公众号目标
2025-08-05 09:11:06,219 - INFO - 📋 共找到 53 个公众号，开始逐个处理...
2025-08-05 09:11:06,219 - INFO - ============================================================
2025-08-05 09:11:06,219 - INFO - 📍 处理第 1/53 个公众号: 钟山清风
2025-08-05 09:11:06,219 - INFO - ============================================================
2025-08-05 09:11:06,219 - INFO - [步骤 1/5] 为 '钟山清风' 创建独立的 Cookie 抓取器...
2025-08-05 09:11:06,219 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-05 09:11:06,219 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-05 09:11:06,219 - INFO - 步骤1: 正在准备网络环境...
2025-08-05 09:11:06,219 - INFO - === 开始重置网络状态 ===
2025-08-05 09:11:06,219 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-05 09:11:06,329 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-05 09:11:06,330 - INFO - 🔧 正在关闭系统代理设置...
2025-08-05 09:11:06,330 - INFO - 系统代理已成功关闭
2025-08-05 09:11:06,330 - INFO - ✅ 代理关闭操作
2025-08-05 09:11:06,330 - INFO - 🔗 正在验证网络连接...
2025-08-05 09:11:12,655 - INFO - ✅ 网络连接正常（无代理）
2025-08-05 09:11:12,656 - INFO - ✅ 网络状态重置验证完成
2025-08-05 09:11:12,656 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-05 09:11:12,657 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-05 09:11:12,657 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider_gitlab\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-05 09:11:16,704 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-05 09:11:16,705 - INFO - 🔄 Cookie抓取器进程已启动，PID: 32916
2025-08-05 09:11:16,705 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-05 09:11:19,705 - INFO - 等待代理服务启动...
2025-08-05 09:11:36,176 - INFO - 代理服务已启动并正常工作
2025-08-05 09:11:36,176 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 32916)
2025-08-05 09:11:36,176 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-05 09:11:36,176 - INFO - [步骤 2/5] 为 '钟山清风' 启动 UI 自动化...
2025-08-05 09:11:36,176 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-05 09:11:36,177 - INFO - 准备将链接发送到文件传输助手...
2025-08-05 09:11:36,177 - INFO - 正在查找微信主窗口...
2025-08-05 09:11:46,884 - WARNING - 未找到 'WeChatMainWndForPC' 窗口，尝试备用方案...
2025-08-05 09:11:49,701 - INFO - 成功找到微信窗口 (Name='微信')
2025-08-05 09:11:49,701 - INFO - 正在激活微信窗口...
2025-08-05 09:11:52,207 - INFO - 微信窗口已激活。
2025-08-05 09:11:52,208 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-05 09:11:58,885 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-05 09:11:58,886 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-05 09:12:02,216 - INFO - 正在查找聊天输入框...
2025-08-05 09:12:04,217 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-05 09:12:04,231 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-05 09:12:04,232 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-05 09:12:05,838 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-05 09:12:08,129 - INFO - 链接已粘贴，正在发送...
2025-08-05 09:12:08,390 - INFO - 找到发送按钮，点击发送...
2025-08-05 09:12:09,196 - INFO - 链接已发送。
2025-08-05 09:12:12,198 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-05 09:12:14,292 - INFO - 已定位到最新的消息项，准备点击。
2025-08-05 09:12:15,018 - INFO - ✅ 成功点击最新链接。
2025-08-05 09:12:18,018 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-05 09:12:18,019 - INFO - 正在查找微信浏览器窗口...
2025-08-05 09:12:18,020 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-05 09:12:18,035 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-05 09:12:20,740 - INFO - 已成功激活焦点浏览器窗口
2025-08-05 09:12:22,241 - INFO - 正在检测SSL证书错误页面...
2025-08-05 09:12:29,444 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-05 09:12:29,444 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-05 09:12:29,444 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-05 09:12:29,444 - INFO - 正在执行第 1 次刷新操作...
2025-08-05 09:12:29,445 - WARNING - 在文件中未找到有效的Cookie数据。
2025-08-05 09:12:29,445 - INFO - 正在查找微信浏览器窗口...
2025-08-05 09:12:29,445 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-05 09:12:29,448 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-05 09:12:32,155 - INFO - 已成功激活焦点浏览器窗口
2025-08-05 09:12:32,655 - INFO - 正在查找微信浏览器窗口...
2025-08-05 09:12:32,656 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-05 09:12:32,665 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-05 09:12:35,369 - INFO - 已成功激活焦点浏览器窗口
2025-08-05 09:12:36,869 - INFO - 正在检测SSL证书错误页面...
2025-08-05 09:12:44,783 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-05 09:12:44,784 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-05 09:12:47,285 - WARNING - 在文件中未找到有效的Cookie数据。
2025-08-05 09:12:47,286 - INFO - 第 1 次刷新完成
2025-08-05 09:12:48,787 - INFO - 正在执行第 2 次刷新操作...
2025-08-05 09:12:48,788 - WARNING - 在文件中未找到有效的Cookie数据。
2025-08-05 09:12:48,788 - INFO - 正在查找微信浏览器窗口...
2025-08-05 09:12:48,789 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-05 09:12:48,799 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-05 09:12:51,503 - INFO - 已成功激活焦点浏览器窗口
2025-08-05 09:12:52,004 - INFO - 正在查找微信浏览器窗口...
2025-08-05 09:12:52,005 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-05 09:12:52,013 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-05 09:12:54,718 - INFO - 已成功激活焦点浏览器窗口
2025-08-05 09:12:56,219 - INFO - 正在检测SSL证书错误页面...
2025-08-05 09:13:04,127 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-05 09:13:04,128 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-05 09:13:06,629 - WARNING - 在文件中未找到有效的Cookie数据。
2025-08-05 09:13:06,630 - INFO - 第 2 次刷新完成
2025-08-05 09:13:08,131 - INFO - 正在执行第 3 次刷新操作...
2025-08-05 09:13:08,131 - WARNING - 在文件中未找到有效的Cookie数据。
2025-08-05 09:13:08,131 - INFO - 正在查找微信浏览器窗口...
2025-08-05 09:13:08,132 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-05 09:13:08,137 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-05 09:13:10,842 - INFO - 已成功激活焦点浏览器窗口
2025-08-05 09:13:11,343 - INFO - 正在查找微信浏览器窗口...
2025-08-05 09:13:11,344 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-05 09:13:11,350 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-05 09:13:14,054 - INFO - 已成功激活焦点浏览器窗口
2025-08-05 09:13:15,556 - INFO - 正在检测SSL证书错误页面...
2025-08-05 09:13:23,464 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-05 09:13:23,465 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-05 09:13:25,966 - WARNING - 在文件中未找到有效的Cookie数据。
2025-08-05 09:13:25,967 - INFO - 第 3 次刷新完成
2025-08-05 09:13:25,968 - WARNING - 在文件中未找到有效的Cookie数据。
2025-08-05 09:13:25,968 - INFO - ✅ 自动刷新操作全部完成
2025-08-05 09:13:25,969 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-05 09:13:25,970 - INFO - [步骤 3/5] 等待 '钟山清风' 的 Cookie 数据...
2025-08-05 09:13:25,970 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-05 09:13:26,971 - INFO - 检测到Cookie文件已生成。
2025-08-05 09:13:26,972 - WARNING - 在文件中未找到有效的Cookie数据。
2025-08-05 09:13:26,972 - ERROR - ❌ 公众号 '钟山清风' Cookie 解析失败
2025-08-05 09:13:26,972 - ERROR - 💡 可能的原因:
2025-08-05 09:13:26,973 - ERROR -    1. mitmproxy 没有成功抓取到微信请求
2025-08-05 09:13:26,973 - ERROR -    2. 微信内置浏览器没有正确打开链接
2025-08-05 09:13:26,973 - ERROR -    3. 网络连接问题或代理设置问题
2025-08-05 09:13:26,974 - ERROR - 💡 建议:
2025-08-05 09:13:26,974 - ERROR -    1. 检查微信是否正常打开了文章链接
2025-08-05 09:13:26,974 - ERROR -    2. 手动在微信中刷新文章页面
2025-08-05 09:13:26,974 - ERROR -    3. 确保网络连接正常
2025-08-05 09:13:26,974 - INFO - 🧹 开始清理抓取器资源...
2025-08-05 09:13:26,975 - INFO - 正在停止Cookie抓取器 (PID: 32916)...
2025-08-05 09:13:26,977 - INFO - Cookie抓取器已成功终止。
2025-08-05 09:13:26,977 - INFO - 正在验证并清理代理设置...
2025-08-05 09:13:26,978 - INFO - === 开始重置网络状态 ===
2025-08-05 09:13:26,979 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-05 09:13:27,080 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-05 09:13:27,080 - INFO - 🔧 正在关闭系统代理设置...
2025-08-05 09:13:27,081 - INFO - 系统代理已成功关闭
2025-08-05 09:13:27,081 - INFO - ✅ 代理关闭操作
2025-08-05 09:13:27,081 - INFO - 🔗 正在验证网络连接...
2025-08-05 09:13:32,276 - INFO - ✅ 网络连接正常（无代理）
2025-08-05 09:13:32,277 - INFO - ✅ 网络状态重置验证完成
2025-08-05 09:13:32,277 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-05 09:13:47,304 - ERROR - ❌ 网络连接异常: HTTPSConnectionPool(host='httpbin.org', port=443): Max retries exceeded with url: /ip (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001E0AFDEF890>, 'Connection to httpbin.org timed out. (connect timeout=5)'))
2025-08-05 09:13:47,305 - WARNING - ⚠️ 网络连接验证失败，可能需要手动检查
2025-08-05 09:13:47,305 - INFO - ============================================================
2025-08-05 09:13:47,306 - INFO - 📍 处理第 2/53 个公众号: 南京党建
2025-08-05 09:13:47,306 - INFO - ============================================================
2025-08-05 09:13:47,307 - INFO - [步骤 1/5] 为 '南京党建' 创建独立的 Cookie 抓取器...
2025-08-05 09:13:47,308 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-05 09:13:47,309 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-05 09:13:47,309 - INFO - 步骤1: 正在准备网络环境...
2025-08-05 09:13:47,310 - INFO - === 开始重置网络状态 ===
2025-08-05 09:13:47,310 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-05 09:13:47,424 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-05 09:13:47,424 - INFO - 🔧 正在关闭系统代理设置...
2025-08-05 09:13:47,424 - INFO - 系统代理已成功关闭
2025-08-05 09:13:47,424 - INFO - ✅ 代理关闭操作
2025-08-05 09:13:47,424 - INFO - 🔗 正在验证网络连接...
2025-08-05 09:13:48,275 - INFO - ✅ 网络连接正常（无代理）
2025-08-05 09:13:48,276 - INFO - ✅ 网络状态重置验证完成
2025-08-05 09:13:48,277 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-05 09:13:48,278 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-05 09:13:48,279 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider_gitlab\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-05 09:13:49,701 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-05 09:13:49,702 - INFO - 🔄 Cookie抓取器进程已启动，PID: 25340
2025-08-05 09:13:49,702 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-05 09:13:52,702 - INFO - 等待代理服务启动...
2025-08-05 09:13:53,147 - INFO - 代理服务已启动并正常工作
2025-08-05 09:13:53,147 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 25340)
2025-08-05 09:13:53,148 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-05 09:13:53,148 - INFO - [步骤 2/5] 为 '南京党建' 启动 UI 自动化...
2025-08-05 09:13:53,149 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-05 09:13:53,149 - INFO - 准备将链接发送到文件传输助手...
2025-08-05 09:13:53,149 - INFO - 正在查找微信主窗口...
2025-08-05 09:13:53,177 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-05 09:13:53,177 - INFO - 正在激活微信窗口...
2025-08-05 09:13:55,689 - INFO - 微信窗口已激活。
2025-08-05 09:13:55,689 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-05 09:14:02,318 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-05 09:14:02,319 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-05 09:14:05,646 - INFO - 正在查找聊天输入框...
2025-08-05 09:14:07,647 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-05 09:14:07,659 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-05 09:14:07,659 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-05 09:14:09,233 - INFO - 已将链接复制到剪贴板: http://mp.weixin.qq.com/s?__biz=MzI2OTMwNzU5Nw==&mid=2247686085&idx=3&sn=55bd6760980f3b629264d1854f8a8385&chksm=eaee0c67dd9985714f28c6bffb0e6dd9eb56b7c2d2bba40844197253640266a5aa97aee8466d#rd
2025-08-05 09:14:11,524 - INFO - 链接已粘贴，正在发送...
2025-08-05 09:14:11,767 - INFO - 找到发送按钮，点击发送...
2025-08-05 09:14:12,582 - INFO - 链接已发送。
2025-08-05 09:14:15,583 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-05 09:14:17,678 - INFO - 已定位到最新的消息项，准备点击。
2025-08-05 09:14:18,415 - INFO - ✅ 成功点击最新链接。
2025-08-05 09:14:21,416 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-05 09:14:21,416 - INFO - 正在查找微信浏览器窗口...
2025-08-05 09:14:21,416 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-05 09:14:21,424 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-05 09:14:24,130 - INFO - 已成功激活焦点浏览器窗口
2025-08-05 09:14:25,631 - INFO - 正在检测SSL证书错误页面...
2025-08-05 09:14:33,092 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-05 09:14:33,093 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-05 09:14:33,093 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-05 09:14:33,093 - INFO - 正在执行第 1 次刷新操作...
2025-08-05 09:14:33,093 - INFO - 从文件中解析到有效Cookie数据。
2025-08-05 09:14:33,093 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-05 09:14:33,093 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-05 09:14:33,094 - INFO - 从文件中解析到有效Cookie数据。
2025-08-05 09:14:33,094 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-05 09:14:33,094 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-05 09:14:33,095 - INFO - [步骤 3/5] 等待 '南京党建' 的 Cookie 数据...
2025-08-05 09:14:33,095 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-05 09:14:34,095 - INFO - 检测到Cookie文件已生成。
2025-08-05 09:14:34,096 - INFO - 从文件中解析到有效Cookie数据。
2025-08-05 09:14:34,097 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-05 09:14:34,097 - INFO - [步骤 4/5] 停止 '南京党建' 的 Cookie 抓取器...
2025-08-05 09:14:34,098 - INFO - 🧹 开始清理抓取器资源...
2025-08-05 09:14:34,098 - INFO - 正在停止Cookie抓取器 (PID: 25340)...
2025-08-05 09:14:34,100 - INFO - Cookie抓取器已成功终止。
2025-08-05 09:14:34,101 - INFO - 正在验证并清理代理设置...
2025-08-05 09:14:34,101 - INFO - === 开始重置网络状态 ===
2025-08-05 09:14:34,102 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-05 09:14:34,216 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-05 09:14:34,216 - INFO - 🔧 正在关闭系统代理设置...
2025-08-05 09:14:34,216 - INFO - 系统代理已成功关闭
2025-08-05 09:14:34,216 - INFO - ✅ 代理关闭操作
2025-08-05 09:14:34,217 - INFO - 🔗 正在验证网络连接...
2025-08-05 09:14:45,083 - INFO - ✅ 网络连接正常（无代理）
2025-08-05 09:14:45,084 - INFO - ✅ 网络状态重置验证完成
2025-08-05 09:14:45,084 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-05 09:14:50,902 - INFO - ✅ 网络连接正常（无代理）
2025-08-05 09:14:50,902 - INFO - ✅ 网络连接验证正常
2025-08-05 09:14:53,903 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-05 09:14:53,903 - INFO - [步骤 5/5] 开始爬取 '南京党建' 的文章...
2025-08-05 09:14:53,903 - INFO - 🔄 第 1/2 次尝试爬取...
2025-08-05 09:15:18,158 - WARNING - ⚠️ Cookie验证失败（ret=-3），准备重新抓取Cookie...
2025-08-05 09:15:18,158 - INFO - 🔄 重新启动Cookie抓取器...
2025-08-05 09:15:18,159 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-05 09:15:18,160 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-05 09:15:18,161 - INFO - 步骤1: 正在准备网络环境...
2025-08-05 09:15:18,162 - INFO - === 开始重置网络状态 ===
2025-08-05 09:15:18,162 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-05 09:15:18,271 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-05 09:15:18,271 - INFO - 🔧 正在关闭系统代理设置...
2025-08-05 09:15:18,272 - INFO - 系统代理已成功关闭
2025-08-05 09:15:18,272 - INFO - ✅ 代理关闭操作
2025-08-05 09:15:18,272 - INFO - 🔗 正在验证网络连接...
2025-08-05 09:15:25,129 - INFO - ✅ 网络连接正常（无代理）
2025-08-05 09:15:25,130 - INFO - ✅ 网络状态重置验证完成
2025-08-05 09:15:25,130 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-05 09:15:25,131 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-05 09:15:25,132 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider_gitlab\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-05 09:15:27,281 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-05 09:15:27,282 - INFO - 🔄 Cookie抓取器进程已启动，PID: 42356
2025-08-05 09:15:27,282 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-05 09:15:30,282 - INFO - 等待代理服务启动...
2025-08-05 09:16:02,411 - ERROR - 代理服务启动超时
2025-08-05 09:16:02,412 - ERROR - ❌ 代理服务无法正常启动
2025-08-05 09:16:02,412 - ERROR - 进程仍在运行，但代理服务无响应
2025-08-05 09:16:02,413 - INFO - 🧹 开始清理抓取器资源...
2025-08-05 09:16:02,413 - INFO - 正在停止Cookie抓取器 (PID: 42356)...
2025-08-05 09:16:02,416 - INFO - Cookie抓取器已成功终止。
2025-08-05 09:16:02,416 - INFO - 正在验证并清理代理设置...
2025-08-05 09:16:02,416 - INFO - === 开始重置网络状态 ===
2025-08-05 09:16:02,417 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-05 09:16:02,533 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-05 09:16:02,533 - INFO - 🔧 正在关闭系统代理设置...
2025-08-05 09:16:02,533 - INFO - 系统代理已成功关闭
2025-08-05 09:16:02,533 - INFO - ✅ 代理关闭操作
2025-08-05 09:16:02,533 - INFO - 🔗 正在验证网络连接...
2025-08-05 09:16:13,737 - INFO - ✅ 网络连接正常（无代理）
2025-08-05 09:16:13,738 - INFO - ✅ 网络状态重置验证完成
2025-08-05 09:16:13,738 - INFO - ✅ 代理已完全关闭，网络状态已清理
